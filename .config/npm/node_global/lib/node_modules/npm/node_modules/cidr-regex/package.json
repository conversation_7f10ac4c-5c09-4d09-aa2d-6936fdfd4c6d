{"name": "cidr-regex", "version": "4.1.1", "description": "Regular expression for matching IP addresses in CIDR notation", "author": "silverwind <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (http://flipjs.io/)"], "repository": "silverwind/cidr-regex", "license": "BSD-2-<PERSON><PERSON>", "type": "module", "sideEffects": false, "main": "./dist/index.js", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "dependencies": {"ip-regex": "^5.0.0"}, "devDependencies": {"@types/node": "20.12.12", "eslint": "8.57.0", "eslint-config-silverwind": "85.1.4", "eslint-config-silverwind-typescript": "3.2.7", "typescript": "5.4.5", "typescript-config-silverwind": "4.3.2", "updates": "16.1.1", "versions": "12.0.2", "vite": "5.2.11", "vite-config-silverwind": "1.1.2", "vite-plugin-dts": "3.9.1", "vitest": "1.6.0", "vitest-config-silverwind": "9.0.6"}}