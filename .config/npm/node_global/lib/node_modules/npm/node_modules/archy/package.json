{"name": "archy", "version": "1.0.0", "description": "render nested hierarchies `npm ls` style with unicode pipes", "main": "index.js", "devDependencies": {"tap": "~0.3.3", "tape": "~0.1.1"}, "scripts": {"test": "tap test"}, "testling": {"files": "test/*.js", "browsers": {"iexplore": ["6.0", "7.0", "8.0", "9.0"], "chrome": ["20.0"], "firefox": ["10.0", "15.0"], "safari": ["5.1"], "opera": ["12.0"]}}, "repository": {"type": "git", "url": "http://github.com/substack/node-archy.git"}, "keywords": ["hierarchy", "npm ls", "unicode", "pretty", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}