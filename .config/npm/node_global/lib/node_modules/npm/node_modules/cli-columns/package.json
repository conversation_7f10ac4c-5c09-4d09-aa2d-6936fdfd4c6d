{"name": "cli-columns", "version": "4.0.0", "description": "Columnated lists for the CLI.", "scripts": {"lint": "npx eslint --fix '*.js' && npx prettier --write '*.js'", "test": "node test.js && node color.js"}, "keywords": ["ansi", "cli", "column", "columnate", "columns", "grid", "list", "log", "ls", "row", "rows", "unicode", "unix"], "author": "<PERSON> <me@shannonmoeller> (http://shannonmoeller.com)", "homepage": "https://github.com/shannonmoeller/cli-columns#readme", "repository": "shannon<PERSON>eller/cli-columns", "license": "MIT", "main": "index.js", "files": ["*.js"], "dependencies": {"string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "devDependencies": {"chalk": "^4.1.2"}, "engines": {"node": ">= 10"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true}, "parserOptions": {"ecmaVersion": 8}}, "prettier": {"singleQuote": true}}