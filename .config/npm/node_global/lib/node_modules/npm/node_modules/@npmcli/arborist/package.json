{"name": "@npmcli/arborist", "version": "7.5.4", "description": "Manage node_modules trees", "dependencies": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/fs": "^3.1.1", "@npmcli/installed-package-contents": "^2.1.0", "@npmcli/map-workspaces": "^3.0.2", "@npmcli/metavuln-calculator": "^7.1.1", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/package-json": "^5.1.0", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^2.0.0", "@npmcli/run-script": "^8.1.0", "bin-links": "^4.0.4", "cacache": "^18.0.3", "common-ancestor-path": "^1.0.1", "hosted-git-info": "^7.0.2", "json-parse-even-better-errors": "^3.0.2", "json-stringify-nice": "^1.1.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "nopt": "^7.2.1", "npm-install-checks": "^6.2.0", "npm-package-arg": "^11.0.2", "npm-pick-manifest": "^9.0.1", "npm-registry-fetch": "^17.0.1", "pacote": "^18.0.6", "parse-conflict-json": "^3.0.0", "proc-log": "^4.2.0", "proggy": "^2.0.0", "promise-all-reject-late": "^1.0.0", "promise-call-limit": "^3.0.1", "read-package-json-fast": "^3.0.2", "semver": "^7.3.7", "ssri": "^10.0.6", "treeverse": "^3.0.0", "walk-up-path": "^3.0.1"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "benchmark": "^2.1.4", "minify-registry-metadata": "^3.0.0", "nock": "^13.3.3", "tap": "^16.3.8", "tar-stream": "^3.0.0", "tcompare": "^5.0.6"}, "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "test-proxy": "ARBORIST_TEST_PROXY=1 tap --snapshot", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run lint -- --fix", "benchmark": "node scripts/benchmark.js", "benchclean": "rm -rf scripts/benchmark/*/", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/arborist"}, "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "main": "lib/index.js", "bin": {"arborist": "bin/index.js"}, "tap": {"after": "test/fixtures/cleanup.js", "test-env": ["LC_ALL=sk"], "timeout": "360", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^16.14.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "content": "../../scripts/template-oss/index.js"}}