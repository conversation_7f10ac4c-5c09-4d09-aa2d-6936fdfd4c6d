{"name": "@npmcli/map-workspaces", "version": "3.0.6", "main": "lib/index.js", "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "description": "Retrieves a name:pathname Map for a given workspaces config", "repository": {"type": "git", "url": "https://github.com/npm/map-workspaces.git"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "workspaces", "map-workspaces"], "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "pretest": "npm run lint", "test": "tap", "snap": "tap", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.0.1"}, "dependencies": {"@npmcli/name-from-folder": "^2.0.0", "glob": "^10.2.2", "minimatch": "^9.0.0", "read-package-json-fast": "^3.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": "true"}}