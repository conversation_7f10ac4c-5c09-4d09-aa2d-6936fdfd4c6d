{"name": "@npmcli/query", "version": "3.1.0", "description": "npm query parser and tools", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "keywords": ["ast", "npm", "npmcli", "parser", "postcss", "postcss-selector-parser", "query"], "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": true}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.2.0"}, "dependencies": {"postcss-selector-parser": "^6.0.10"}, "repository": {"type": "git", "url": "https://github.com/npm/query.git"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}