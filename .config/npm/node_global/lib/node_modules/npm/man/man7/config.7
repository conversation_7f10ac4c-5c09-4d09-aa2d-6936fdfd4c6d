.TH "CONFIG" "7" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBconfig\fR - More than you probably want to know about npm configuration
.SS "Description"
.P
This article details npm configuration in general. To learn about the \fBconfig\fR command, see npm help config.
.P
npm gets its configuration values from the following sources, sorted by priority:
.SS "Command Line Flags"
.P
Putting \fB--foo bar\fR on the command line sets the \fBfoo\fR configuration parameter to \fB"bar"\fR. A \fB--\fR argument tells the cli parser to stop reading flags. Using \fB--flag\fR without specifying any value will set the value to \fBtrue\fR.
.P
Example: \fB--flag1 --flag2\fR will set both configuration parameters to \fBtrue\fR, while \fB--flag1 --flag2 bar\fR will set \fBflag1\fR to \fBtrue\fR, and \fBflag2\fR to \fBbar\fR. Finally, \fB--flag1 --flag2 -- bar\fR will set both configuration parameters to \fBtrue\fR, and the \fBbar\fR is taken as a command argument.
.SS "Environment Variables"
.P
Any environment variables that start with \fBnpm_config_\fR will be interpreted as a configuration parameter. For example, putting \fBnpm_config_foo=bar\fR in your environment will set the \fBfoo\fR configuration parameter to \fBbar\fR. Any environment configurations that are not given a value will be given the value of \fBtrue\fR. Config values are case-insensitive, so \fBNPM_CONFIG_FOO=bar\fR will work the same. However, please note that inside npm help scripts npm will set its own environment variables and Node will prefer those lowercase versions over any uppercase ones that you might set. For details see \fBthis issue\fR \fI\(lahttps://github.com/npm/npm/issues/14528\(ra\fR.
.P
Notice that you need to use underscores instead of dashes, so \fB--allow-same-version\fR would become \fBnpm_config_allow_same_version=true\fR.
.SS "npmrc Files"
.P
The four relevant files are:
.RS 0
.IP \(bu 4
per-project configuration file (\fB/path/to/my/project/.npmrc\fR)
.IP \(bu 4
per-user configuration file (defaults to \fB$HOME/.npmrc\fR; configurable via CLI option \fB--userconfig\fR or environment variable \fB$NPM_CONFIG_USERCONFIG\fR)
.IP \(bu 4
global configuration file (defaults to \fB$PREFIX/etc/npmrc\fR; configurable via CLI option \fB--globalconfig\fR or environment variable \fB$NPM_CONFIG_GLOBALCONFIG\fR)
.IP \(bu 4
npm's built-in configuration file (\fB/path/to/npm/npmrc\fR)
.RE 0

.P
See npm help npmrc for more details.
.SS "Default Configs"
.P
Run \fBnpm config ls -l\fR to see a set of configuration parameters that are internal to npm, and are defaults if nothing else is specified.
.SS "Shorthands and Other CLI Niceties"
.P
The following shorthands are parsed on the command-line:
.RS 0
.IP \(bu 4
\fB-a\fR: \fB--all\fR
.IP \(bu 4
\fB--enjoy-by\fR: \fB--before\fR
.IP \(bu 4
\fB-c\fR: \fB--call\fR
.IP \(bu 4
\fB--desc\fR: \fB--description\fR
.IP \(bu 4
\fB-f\fR: \fB--force\fR
.IP \(bu 4
\fB-g\fR: \fB--global\fR
.IP \(bu 4
\fB--iwr\fR: \fB--include-workspace-root\fR
.IP \(bu 4
\fB-L\fR: \fB--location\fR
.IP \(bu 4
\fB-d\fR: \fB--loglevel info\fR
.IP \(bu 4
\fB-s\fR: \fB--loglevel silent\fR
.IP \(bu 4
\fB--silent\fR: \fB--loglevel silent\fR
.IP \(bu 4
\fB--ddd\fR: \fB--loglevel silly\fR
.IP \(bu 4
\fB--dd\fR: \fB--loglevel verbose\fR
.IP \(bu 4
\fB--verbose\fR: \fB--loglevel verbose\fR
.IP \(bu 4
\fB-q\fR: \fB--loglevel warn\fR
.IP \(bu 4
\fB--quiet\fR: \fB--loglevel warn\fR
.IP \(bu 4
\fB-l\fR: \fB--long\fR
.IP \(bu 4
\fB-m\fR: \fB--message\fR
.IP \(bu 4
\fB--local\fR: \fB--no-global\fR
.IP \(bu 4
\fB-n\fR: \fB--no-yes\fR
.IP \(bu 4
\fB--no\fR: \fB--no-yes\fR
.IP \(bu 4
\fB-p\fR: \fB--parseable\fR
.IP \(bu 4
\fB--porcelain\fR: \fB--parseable\fR
.IP \(bu 4
\fB-C\fR: \fB--prefix\fR
.IP \(bu 4
\fB--readonly\fR: \fB--read-only\fR
.IP \(bu 4
\fB--reg\fR: \fB--registry\fR
.IP \(bu 4
\fB-S\fR: \fB--save\fR
.IP \(bu 4
\fB-B\fR: \fB--save-bundle\fR
.IP \(bu 4
\fB-D\fR: \fB--save-dev\fR
.IP \(bu 4
\fB-E\fR: \fB--save-exact\fR
.IP \(bu 4
\fB-O\fR: \fB--save-optional\fR
.IP \(bu 4
\fB-P\fR: \fB--save-prod\fR
.IP \(bu 4
\fB-?\fR: \fB--usage\fR
.IP \(bu 4
\fB-h\fR: \fB--usage\fR
.IP \(bu 4
\fB-H\fR: \fB--usage\fR
.IP \(bu 4
\fB--help\fR: \fB--usage\fR
.IP \(bu 4
\fB-v\fR: \fB--version\fR
.IP \(bu 4
\fB-w\fR: \fB--workspace\fR
.IP \(bu 4
\fB--ws\fR: \fB--workspaces\fR
.IP \(bu 4
\fB-y\fR: \fB--yes\fR
.RE 0

.P
If the specified configuration param resolves unambiguously to a known configuration parameter, then it is expanded to that configuration parameter. For example:
.P
.RS 2
.nf
npm ls --par
# same as:
npm ls --parseable
.fi
.RE
.P
If multiple single-character shorthands are strung together, and the resulting combination is unambiguously not some other configuration param, then it is expanded to its various component pieces. For example:
.P
.RS 2
.nf
npm ls -gpld
# same as:
npm ls --global --parseable --long --loglevel info
.fi
.RE
.SS "Config Settings"
.SS "\fB_auth\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
A basic-auth string to use when authenticating against the npm registry. This will ONLY be used to authenticate against the npm registry. For other registries you will need to scope it like "//other-registry.tld/:_auth"
.P
Warning: This should generally not be set via a command-line option. It is safer to use a registry-provided authentication bearer token stored in the ~/.npmrc file by running \fBnpm login\fR.
.SS "\fBaccess\fR"
.RS 0
.IP \(bu 4
Default: 'public' for new packages, existing packages it will not change the current level
.IP \(bu 4
Type: null, "restricted", or "public"
.RE 0

.P
If you do not want your scoped package to be publicly viewable (and installable) set \fB--access=restricted\fR.
.P
Unscoped packages can not be set to \fBrestricted\fR.
.P
Note: This defaults to not changing the current access level for existing packages. Specifying a value of \fBrestricted\fR or \fBpublic\fR during publish will change the access for an existing package the same way that \fBnpm access set
status\fR would.
.SS "\fBall\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
When running \fBnpm outdated\fR and \fBnpm ls\fR, setting \fB--all\fR will show all outdated or installed packages, rather than only those directly depended upon by the current project.
.SS "\fBallow-same-version\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Prevents throwing an error when \fBnpm version\fR is used to set the new version to the same value as the current version.
.SS "\fBaudit\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
When "true" submit audit reports alongside the current npm command to the default registry and all registries configured for scopes. See the documentation for npm help audit for details on what is submitted.
.SS "\fBaudit-level\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null, "info", "low", "moderate", "high", "critical", or "none"
.RE 0

.P
The minimum level of vulnerability for \fBnpm audit\fR to exit with a non-zero exit code.
.SS "\fBauth-type\fR"
.RS 0
.IP \(bu 4
Default: "web"
.IP \(bu 4
Type: "legacy" or "web"
.RE 0

.P
What authentication strategy to use with \fBlogin\fR. Note that if an \fBotp\fR config is given, this value will always be set to \fBlegacy\fR.
.SS "\fBbefore\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Date
.RE 0

.P
If passed to \fBnpm install\fR, will rebuild the npm tree such that only versions that were available \fBon or before\fR the \fB--before\fR time get installed. If there's no versions available for the current set of direct dependencies, the command will error.
.P
If the requested version is a \fBdist-tag\fR and the given tag does not pass the \fB--before\fR filter, the most recent version less than or equal to that tag will be used. For example, \fBfoo@latest\fR might install \fBfoo@1.2\fR even though \fBlatest\fR is \fB2.0\fR.
.SS "\fBbin-links\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Tells npm to create symlinks (or \fB.cmd\fR shims on Windows) for package executables.
.P
Set to false to have it not do this. This can be used to work around the fact that some file systems don't support symlinks, even on ostensibly Unix systems.
.SS "\fBbrowser\fR"
.RS 0
.IP \(bu 4
Default: OS X: \fB"open"\fR, Windows: \fB"start"\fR, Others: \fB"xdg-open"\fR
.IP \(bu 4
Type: null, Boolean, or String
.RE 0

.P
The browser that is called by npm commands to open websites.
.P
Set to \fBfalse\fR to suppress browser behavior and instead print urls to terminal.
.P
Set to \fBtrue\fR to use default system URL opener.
.SS "\fBca\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String (can be set multiple times)
.RE 0

.P
The Certificate Authority signing certificate that is trusted for SSL connections to the registry. Values should be in PEM format (Windows calls it "Base-64 encoded X.509 (.CER)") with newlines replaced by the string "\[rs]n". For example:
.P
.RS 2
.nf
ca="-----BEGIN CERTIFICATE-----\[rs]nXXXX\[rs]nXXXX\[rs]n-----END CERTIFICATE-----"
.fi
.RE
.P
Set to \fBnull\fR to only allow "known" registrars, or to a specific CA cert to trust only that specific signing authority.
.P
Multiple CAs can be trusted by specifying an array of certificates:
.P
.RS 2
.nf
ca\[lB]\[rB]="..."
ca\[lB]\[rB]="..."
.fi
.RE
.P
See also the \fBstrict-ssl\fR config.
.SS "\fBcache\fR"
.RS 0
.IP \(bu 4
Default: Windows: \fB%LocalAppData%\[rs]npm-cache\fR, Posix: \fB~/.npm\fR
.IP \(bu 4
Type: Path
.RE 0

.P
The location of npm's cache directory.
.SS "\fBcafile\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: Path
.RE 0

.P
A path to a file containing one or multiple Certificate Authority signing certificates. Similar to the \fBca\fR setting, but allows for multiple CA's, as well as for the CA information to be stored in a file on disk.
.SS "\fBcall\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.RE 0

.P
Optional companion option for \fBnpm exec\fR, \fBnpx\fR that allows for specifying a custom command to be run along with the installed packages.
.P
.RS 2
.nf
npm exec --package yo --package generator-node --call "yo node"
.fi
.RE
.SS "\fBcidr\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String (can be set multiple times)
.RE 0

.P
This is a list of CIDR address to be used when configuring limited access tokens with the \fBnpm token create\fR command.
.SS "\fBcolor\fR"
.RS 0
.IP \(bu 4
Default: true unless the NO_COLOR environ is set to something other than '0'
.IP \(bu 4
Type: "always" or Boolean
.RE 0

.P
If false, never shows colors. If \fB"always"\fR then always shows colors. If true, then only prints color codes for tty file descriptors.
.SS "\fBcommit-hooks\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Run git commit hooks when using the \fBnpm version\fR command.
.SS "\fBcpu\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
Override CPU architecture of native modules to install. Acceptable values are same as \fBcpu\fR field of package.json, which comes from \fBprocess.arch\fR.
.SS "\fBdepth\fR"
.RS 0
.IP \(bu 4
Default: \fBInfinity\fR if \fB--all\fR is set, otherwise \fB1\fR
.IP \(bu 4
Type: null or Number
.RE 0

.P
The depth to go when recursing packages for \fBnpm ls\fR.
.P
If not set, \fBnpm ls\fR will show only the immediate dependencies of the root project. If \fB--all\fR is set, then npm will show all dependencies by default.
.SS "\fBdescription\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Show the description in \fBnpm search\fR
.SS "\fBdiff\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Define arguments to compare in \fBnpm diff\fR.
.SS "\fBdiff-dst-prefix\fR"
.RS 0
.IP \(bu 4
Default: "b/"
.IP \(bu 4
Type: String
.RE 0

.P
Destination prefix to be used in \fBnpm diff\fR output.
.SS "\fBdiff-ignore-all-space\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Ignore whitespace when comparing lines in \fBnpm diff\fR.
.SS "\fBdiff-name-only\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Prints only filenames when using \fBnpm diff\fR.
.SS "\fBdiff-no-prefix\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Do not show any source or destination prefix in \fBnpm diff\fR output.
.P
Note: this causes \fBnpm diff\fR to ignore the \fB--diff-src-prefix\fR and \fB--diff-dst-prefix\fR configs.
.SS "\fBdiff-src-prefix\fR"
.RS 0
.IP \(bu 4
Default: "a/"
.IP \(bu 4
Type: String
.RE 0

.P
Source prefix to be used in \fBnpm diff\fR output.
.SS "\fBdiff-text\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Treat all files as text in \fBnpm diff\fR.
.SS "\fBdiff-unified\fR"
.RS 0
.IP \(bu 4
Default: 3
.IP \(bu 4
Type: Number
.RE 0

.P
The number of lines of context to print in \fBnpm diff\fR.
.SS "\fBdry-run\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Indicates that you don't want npm to make any changes and that it should only report what it would have done. This can be passed into any of the commands that modify your local installation, eg, \fBinstall\fR, \fBupdate\fR, \fBdedupe\fR, \fBuninstall\fR, as well as \fBpack\fR and \fBpublish\fR.
.P
Note: This is NOT honored by other network related commands, eg \fBdist-tags\fR, \fBowner\fR, etc.
.SS "\fBeditor\fR"
.RS 0
.IP \(bu 4
Default: The EDITOR or VISUAL environment variables, or '%SYSTEMROOT%\[rs]notepad.exe' on Windows, or 'vi' on Unix systems
.IP \(bu 4
Type: String
.RE 0

.P
The command to run for \fBnpm edit\fR and \fBnpm config edit\fR.
.SS "\fBengine-strict\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, then npm will stubbornly refuse to install (or even consider installing) any package that claims to not be compatible with the current Node.js version.
.P
This can be overridden by setting the \fB--force\fR flag.
.SS "\fBexpect-result-count\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Number
.RE 0

.P
Tells to expect a specific number of results from the command.
.P
This config can not be used with: \fBexpect-results\fR
.SS "\fBexpect-results\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Tells npm whether or not to expect results from the command. Can be either true (expect some results) or false (expect no results).
.P
This config can not be used with: \fBexpect-result-count\fR
.SS "\fBfetch-retries\fR"
.RS 0
.IP \(bu 4
Default: 2
.IP \(bu 4
Type: Number
.RE 0

.P
The "retries" config for the \fBretry\fR module to use when fetching packages from the registry.
.P
npm will retry idempotent read requests to the registry in the case of network failures or 5xx HTTP errors.
.SS "\fBfetch-retry-factor\fR"
.RS 0
.IP \(bu 4
Default: 10
.IP \(bu 4
Type: Number
.RE 0

.P
The "factor" config for the \fBretry\fR module to use when fetching packages.
.SS "\fBfetch-retry-maxtimeout\fR"
.RS 0
.IP \(bu 4
Default: 60000 (1 minute)
.IP \(bu 4
Type: Number
.RE 0

.P
The "maxTimeout" config for the \fBretry\fR module to use when fetching packages.
.SS "\fBfetch-retry-mintimeout\fR"
.RS 0
.IP \(bu 4
Default: 10000 (10 seconds)
.IP \(bu 4
Type: Number
.RE 0

.P
The "minTimeout" config for the \fBretry\fR module to use when fetching packages.
.SS "\fBfetch-timeout\fR"
.RS 0
.IP \(bu 4
Default: 300000 (5 minutes)
.IP \(bu 4
Type: Number
.RE 0

.P
The maximum amount of time to wait for HTTP requests to complete.
.SS "\fBforce\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Removes various protections against unfortunate side effects, common mistakes, unnecessary performance degradation, and malicious input.
.RS 0
.IP \(bu 4
Allow clobbering non-npm files in global installs.
.IP \(bu 4
Allow the \fBnpm version\fR command to work on an unclean git repository.
.IP \(bu 4
Allow deleting the cache folder with \fBnpm cache clean\fR.
.IP \(bu 4
Allow installing packages that have an \fBengines\fR declaration requiring a different version of npm.
.IP \(bu 4
Allow installing packages that have an \fBengines\fR declaration requiring a different version of \fBnode\fR, even if \fB--engine-strict\fR is enabled.
.IP \(bu 4
Allow \fBnpm audit fix\fR to install modules outside your stated dependency range (including SemVer-major changes).
.IP \(bu 4
Allow unpublishing all versions of a published package.
.IP \(bu 4
Allow conflicting peerDependencies to be installed in the root project.
.IP \(bu 4
Implicitly set \fB--yes\fR during \fBnpm init\fR.
.IP \(bu 4
Allow clobbering existing values in \fBnpm pkg\fR
.IP \(bu 4
Allow unpublishing of entire packages (not just a single version).
.RE 0

.P
If you don't have a clear idea of what you want to do, it is strongly recommended that you do not use this option!
.SS "\fBforeground-scripts\fR"
.RS 0
.IP \(bu 4
Default: \fBfalse\fR unless when using \fBnpm pack\fR or \fBnpm publish\fR where it defaults to \fBtrue\fR
.IP \(bu 4
Type: Boolean
.RE 0

.P
Run all build scripts (ie, \fBpreinstall\fR, \fBinstall\fR, and \fBpostinstall\fR) scripts for installed packages in the foreground process, sharing standard input, output, and error with the main npm process.
.P
Note that this will generally make installs run slower, and be much noisier, but can be useful for debugging.
.SS "\fBformat-package-lock\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Format \fBpackage-lock.json\fR or \fBnpm-shrinkwrap.json\fR as a human readable file.
.SS "\fBfund\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
When "true" displays the message at the end of each \fBnpm install\fR acknowledging the number of dependencies looking for funding. See npm help fund for details.
.SS "\fBgit\fR"
.RS 0
.IP \(bu 4
Default: "git"
.IP \(bu 4
Type: String
.RE 0

.P
The command to use for git commands. If git is installed on the computer, but is not in the \fBPATH\fR, then set this to the full path to the git binary.
.SS "\fBgit-tag-version\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Tag the commit when using the \fBnpm version\fR command. Setting this to false results in no commit being made at all.
.SS "\fBglobal\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Operates in "global" mode, so that packages are installed into the \fBprefix\fR folder instead of the current working directory. See npm help folders for more on the differences in behavior.
.RS 0
.IP \(bu 4
packages are installed into the \fB{prefix}/lib/node_modules\fR folder, instead of the current working directory.
.IP \(bu 4
bin files are linked to \fB{prefix}/bin\fR
.IP \(bu 4
man pages are linked to \fB{prefix}/share/man\fR
.RE 0

.SS "\fBglobalconfig\fR"
.RS 0
.IP \(bu 4
Default: The global --prefix setting plus 'etc/npmrc'. For example, '/usr/local/etc/npmrc'
.IP \(bu 4
Type: Path
.RE 0

.P
The config file to read for global config options.
.SS "\fBheading\fR"
.RS 0
.IP \(bu 4
Default: "npm"
.IP \(bu 4
Type: String
.RE 0

.P
The string that starts all the debugging log output.
.SS "\fBhttps-proxy\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or URL
.RE 0

.P
A proxy to use for outgoing https requests. If the \fBHTTPS_PROXY\fR or \fBhttps_proxy\fR or \fBHTTP_PROXY\fR or \fBhttp_proxy\fR environment variables are set, proxy settings will be honored by the underlying \fBmake-fetch-happen\fR library.
.SS "\fBif-present\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, npm will not exit with an error code when \fBrun-script\fR is invoked for a script that isn't defined in the \fBscripts\fR section of \fBpackage.json\fR. This option can be used when it's desirable to optionally run a script when it's present and fail if the script fails. This is useful, for example, when running scripts that may only apply for some builds in an otherwise generic CI setup.
.P
This value is not exported to the environment for child processes.
.SS "\fBignore-scripts\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, npm does not run scripts specified in package.json files.
.P
Note that commands explicitly intended to run a particular script, such as \fBnpm start\fR, \fBnpm stop\fR, \fBnpm restart\fR, \fBnpm test\fR, and \fBnpm run-script\fR will still run their intended script if \fBignore-scripts\fR is set, but they will \fInot\fR run any pre- or post-scripts.
.SS "\fBinclude\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: "prod", "dev", "optional", or "peer" (can be set multiple times)
.RE 0

.P
Option that allows for defining which types of dependencies to install.
.P
This is the inverse of \fB--omit=<type>\fR.
.P
Dependency types specified in \fB--include\fR will not be omitted, regardless of the order in which omit/include are specified on the command-line.
.SS "\fBinclude-staged\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Allow installing "staged" published packages, as defined by \fBnpm RFC PR #92\fR \fI\(lahttps://github.com/npm/rfcs/pull/92\(ra\fR.
.P
This is experimental, and not implemented by the npm public registry.
.SS "\fBinclude-workspace-root\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Include the workspace root when workspaces are enabled for a command.
.P
When false, specifying individual workspaces via the \fBworkspace\fR config, or all workspaces via the \fBworkspaces\fR flag, will cause npm to operate only on the specified workspaces, and not on the root project.
.P
This value is not exported to the environment for child processes.
.SS "\fBinit-author-email\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.RE 0

.P
The value \fBnpm init\fR should use by default for the package author's email.
.SS "\fBinit-author-name\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.RE 0

.P
The value \fBnpm init\fR should use by default for the package author's name.
.SS "\fBinit-author-url\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: "" or URL
.RE 0

.P
The value \fBnpm init\fR should use by default for the package author's homepage.
.SS "\fBinit-license\fR"
.RS 0
.IP \(bu 4
Default: "ISC"
.IP \(bu 4
Type: String
.RE 0

.P
The value \fBnpm init\fR should use by default for the package license.
.SS "\fBinit-module\fR"
.RS 0
.IP \(bu 4
Default: "~/.npm-init.js"
.IP \(bu 4
Type: Path
.RE 0

.P
A module that will be loaded by the \fBnpm init\fR command. See the documentation for the \fBinit-package-json\fR \fI\(lahttps://github.com/npm/init-package-json\(ra\fR module for more information, or npm help init.
.SS "\fBinit-version\fR"
.RS 0
.IP \(bu 4
Default: "1.0.0"
.IP \(bu 4
Type: SemVer string
.RE 0

.P
The value that \fBnpm init\fR should use by default for the package version number, if not already set in package.json.
.SS "\fBinstall-links\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
When set file: protocol dependencies will be packed and installed as regular dependencies instead of creating a symlink. This option has no effect on workspaces.
.SS "\fBinstall-strategy\fR"
.RS 0
.IP \(bu 4
Default: "hoisted"
.IP \(bu 4
Type: "hoisted", "nested", "shallow", or "linked"
.RE 0

.P
Sets the strategy for installing packages in node_modules. hoisted (default): Install non-duplicated in top-level, and duplicated as necessary within directory structure. nested: (formerly --legacy-bundling) install in place, no hoisting. shallow (formerly --global-style) only install direct deps at top-level. linked: (experimental) install in node_modules/.store, link in place, unhoisted.
.SS "\fBjson\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Whether or not to output JSON data, rather than the normal output.
.RS 0
.IP \(bu 4
In \fBnpm pkg set\fR it enables parsing set values with JSON.parse() before saving them to your \fBpackage.json\fR.
.RE 0

.P
Not supported by all npm commands.
.SS "\fBlegacy-peer-deps\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Causes npm to completely ignore \fBpeerDependencies\fR when building a package tree, as in npm versions 3 through 6.
.P
If a package cannot be installed because of overly strict \fBpeerDependencies\fR that collide, it provides a way to move forward resolving the situation.
.P
This differs from \fB--omit=peer\fR, in that \fB--omit=peer\fR will avoid unpacking \fBpeerDependencies\fR on disk, but will still design a tree such that \fBpeerDependencies\fR \fIcould\fR be unpacked in a correct place.
.P
Use of \fBlegacy-peer-deps\fR is not recommended, as it will not enforce the \fBpeerDependencies\fR contract that meta-dependencies may rely on.
.SS "\fBlibc\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
Override libc of native modules to install. Acceptable values are same as \fBlibc\fR field of package.json
.SS "\fBlink\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Used with \fBnpm ls\fR, limiting output to only those packages that are linked.
.SS "\fBlocal-address\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: IP Address
.RE 0

.P
The IP address of the local interface to use when making connections to the npm registry. Must be IPv4 in versions of Node prior to 0.12.
.SS "\fBlocation\fR"
.RS 0
.IP \(bu 4
Default: "user" unless \fB--global\fR is passed, which will also set this value to "global"
.IP \(bu 4
Type: "global", "user", or "project"
.RE 0

.P
When passed to \fBnpm config\fR this refers to which config file to use.
.P
When set to "global" mode, packages are installed into the \fBprefix\fR folder instead of the current working directory. See npm help folders for more on the differences in behavior.
.RS 0
.IP \(bu 4
packages are installed into the \fB{prefix}/lib/node_modules\fR folder, instead of the current working directory.
.IP \(bu 4
bin files are linked to \fB{prefix}/bin\fR
.IP \(bu 4
man pages are linked to \fB{prefix}/share/man\fR
.RE 0

.SS "\fBlockfile-version\fR"
.RS 0
.IP \(bu 4
Default: Version 3 if no lockfile, auto-converting v1 lockfiles to v3, otherwise maintain current lockfile version.
.IP \(bu 4
Type: null, 1, 2, 3, "1", "2", or "3"
.RE 0

.P
Set the lockfile format version to be used in package-lock.json and npm-shrinkwrap-json files. Possible options are:
.P
1: The lockfile version used by npm versions 5 and 6. Lacks some data that is used during the install, resulting in slower and possibly less deterministic installs. Prevents lockfile churn when interoperating with older npm versions.
.P
2: The default lockfile version used by npm version 7 and 8. Includes both the version 1 lockfile data and version 3 lockfile data, for maximum determinism and interoperability, at the expense of more bytes on disk.
.P
3: Only the new lockfile information introduced in npm version 7. Smaller on disk than lockfile version 2, but not interoperable with older npm versions. Ideal if all users are on npm version 7 and higher.
.SS "\fBloglevel\fR"
.RS 0
.IP \(bu 4
Default: "notice"
.IP \(bu 4
Type: "silent", "error", "warn", "notice", "http", "info", "verbose", or "silly"
.RE 0

.P
What level of logs to report. All logs are written to a debug log, with the path to that file printed if the execution of a command fails.
.P
Any logs of a higher level than the setting are shown. The default is "notice".
.P
See also the \fBforeground-scripts\fR config.
.SS "\fBlogs-dir\fR"
.RS 0
.IP \(bu 4
Default: A directory named \fB_logs\fR inside the cache
.IP \(bu 4
Type: null or Path
.RE 0

.P
The location of npm's log directory. See npm help logging for more information.
.SS "\fBlogs-max\fR"
.RS 0
.IP \(bu 4
Default: 10
.IP \(bu 4
Type: Number
.RE 0

.P
The maximum number of log files to store.
.P
If set to 0, no log files will be written for the current run.
.SS "\fBlong\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Show extended information in \fBls\fR, \fBsearch\fR, and \fBhelp-search\fR.
.SS "\fBmaxsockets\fR"
.RS 0
.IP \(bu 4
Default: 15
.IP \(bu 4
Type: Number
.RE 0

.P
The maximum number of connections to use per origin (protocol/host/port combination).
.SS "\fBmessage\fR"
.RS 0
.IP \(bu 4
Default: "%s"
.IP \(bu 4
Type: String
.RE 0

.P
Commit message which is used by \fBnpm version\fR when creating version commit.
.P
Any "%s" in the message will be replaced with the version number.
.SS "\fBnode-options\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
Options to pass through to Node.js via the \fBNODE_OPTIONS\fR environment variable. This does not impact how npm itself is executed but it does impact how lifecycle scripts are called.
.SS "\fBnoproxy\fR"
.RS 0
.IP \(bu 4
Default: The value of the NO_PROXY environment variable
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Domain extensions that should bypass any proxies.
.P
Also accepts a comma-delimited string.
.SS "\fBoffline\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Force offline mode: no network requests will be done during install. To allow the CLI to fill in missing cache data, see \fB--prefer-offline\fR.
.SS "\fBomit\fR"
.RS 0
.IP \(bu 4
Default: 'dev' if the \fBNODE_ENV\fR environment variable is set to 'production', otherwise empty.
.IP \(bu 4
Type: "dev", "optional", or "peer" (can be set multiple times)
.RE 0

.P
Dependency types to omit from the installation tree on disk.
.P
Note that these dependencies \fIare\fR still resolved and added to the \fBpackage-lock.json\fR or \fBnpm-shrinkwrap.json\fR file. They are just not physically installed on disk.
.P
If a package type appears in both the \fB--include\fR and \fB--omit\fR lists, then it will be included.
.P
If the resulting omit list includes \fB'dev'\fR, then the \fBNODE_ENV\fR environment variable will be set to \fB'production'\fR for all lifecycle scripts.
.SS "\fBomit-lockfile-registry-resolved\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
This option causes npm to create lock files without a \fBresolved\fR key for registry dependencies. Subsequent installs will need to resolve tarball endpoints with the configured registry, likely resulting in a longer install time.
.SS "\fBos\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
Override OS of native modules to install. Acceptable values are same as \fBos\fR field of package.json, which comes from \fBprocess.platform\fR.
.SS "\fBotp\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
This is a one-time password from a two-factor authenticator. It's needed when publishing or changing package permissions with \fBnpm access\fR.
.P
If not set, and a registry response fails with a challenge for a one-time password, npm will prompt on the command line for one.
.SS "\fBpack-destination\fR"
.RS 0
.IP \(bu 4
Default: "."
.IP \(bu 4
Type: String
.RE 0

.P
Directory in which \fBnpm pack\fR will save tarballs.
.SS "\fBpackage\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
The package or packages to install for npm help exec
.SS "\fBpackage-lock\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to false, then ignore \fBpackage-lock.json\fR files when installing. This will also prevent \fIwriting\fR \fBpackage-lock.json\fR if \fBsave\fR is true.
.SS "\fBpackage-lock-only\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, the current operation will only use the \fBpackage-lock.json\fR, ignoring \fBnode_modules\fR.
.P
For \fBupdate\fR this means only the \fBpackage-lock.json\fR will be updated, instead of checking \fBnode_modules\fR and downloading dependencies.
.P
For \fBlist\fR this means the output will be based on the tree described by the \fBpackage-lock.json\fR, rather than the contents of \fBnode_modules\fR.
.SS "\fBparseable\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Output parseable results from commands that write to standard output. For \fBnpm search\fR, this will be tab-separated table format.
.SS "\fBprefer-dedupe\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Prefer to deduplicate packages if possible, rather than choosing a newer version of a dependency.
.SS "\fBprefer-offline\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, staleness checks for cached data will be bypassed, but missing data will be requested from the server. To force full offline mode, use \fB--offline\fR.
.SS "\fBprefer-online\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, staleness checks for cached data will be forced, making the CLI look for updates immediately even for fresh package data.
.SS "\fBprefix\fR"
.RS 0
.IP \(bu 4
Default: In global mode, the folder where the node executable is installed. Otherwise, the nearest parent folder containing either a package.json file or a node_modules folder.
.IP \(bu 4
Type: Path
.RE 0

.P
The location to install global items. If set on the command line, then it forces non-global commands to run in the specified folder.
.SS "\fBpreid\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.RE 0

.P
The "prerelease identifier" to use as a prefix for the "prerelease" part of a semver. Like the \fBrc\fR in \fB1.2.0-rc.8\fR.
.SS "\fBprogress\fR"
.RS 0
.IP \(bu 4
Default: \fBtrue\fR unless running in a known CI system
.IP \(bu 4
Type: Boolean
.RE 0

.P
When set to \fBtrue\fR, npm will display a progress bar during time intensive operations, if \fBprocess.stderr\fR and \fBprocess.stdout\fR are a TTY.
.P
Set to \fBfalse\fR to suppress the progress bar.
.SS "\fBprovenance\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
When publishing from a supported cloud CI/CD system, the package will be publicly linked to where it was built and published from.
.P
This config can not be used with: \fBprovenance-file\fR
.SS "\fBprovenance-file\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: Path
.RE 0

.P
When publishing, the provenance bundle at the given path will be used.
.P
This config can not be used with: \fBprovenance\fR
.SS "\fBproxy\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null, false, or URL
.RE 0

.P
A proxy to use for outgoing http requests. If the \fBHTTP_PROXY\fR or \fBhttp_proxy\fR environment variables are set, proxy settings will be honored by the underlying \fBrequest\fR library.
.SS "\fBread-only\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
This is used to mark a token as unable to publish when configuring limited access tokens with the \fBnpm token create\fR command.
.SS "\fBrebuild-bundle\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Rebuild bundled dependencies after installation.
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "\fBreplace-registry-host\fR"
.RS 0
.IP \(bu 4
Default: "npmjs"
.IP \(bu 4
Type: "npmjs", "never", "always", or String
.RE 0

.P
Defines behavior for replacing the registry host in a lockfile with the configured registry.
.P
The default behavior is to replace package dist URLs from the default registry (https://registry.npmjs.org) to the configured registry. If set to "never", then use the registry value. If set to "always", then replace the registry host with the configured host every time.
.P
You may also specify a bare hostname (e.g., "registry.npmjs.org").
.SS "\fBsave\fR"
.RS 0
.IP \(bu 4
Default: \fBtrue\fR unless when using \fBnpm update\fR where it defaults to \fBfalse\fR
.IP \(bu 4
Type: Boolean
.RE 0

.P
Save installed packages to a \fBpackage.json\fR file as dependencies.
.P
When used with the \fBnpm rm\fR command, removes the dependency from \fBpackage.json\fR.
.P
Will also prevent writing to \fBpackage-lock.json\fR if set to \fBfalse\fR.
.SS "\fBsave-bundle\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If a package would be saved at install time by the use of \fB--save\fR, \fB--save-dev\fR, or \fB--save-optional\fR, then also put it in the \fBbundleDependencies\fR list.
.P
Ignored if \fB--save-peer\fR is set, since peerDependencies cannot be bundled.
.SS "\fBsave-dev\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Save installed packages to a package.json file as \fBdevDependencies\fR.
.SS "\fBsave-exact\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Dependencies saved to package.json will be configured with an exact version rather than using npm's default semver range operator.
.SS "\fBsave-optional\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Save installed packages to a package.json file as \fBoptionalDependencies\fR.
.SS "\fBsave-peer\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Save installed packages to a package.json file as \fBpeerDependencies\fR
.SS "\fBsave-prefix\fR"
.RS 0
.IP \(bu 4
Default: "^"
.IP \(bu 4
Type: String
.RE 0

.P
Configure how versions of packages installed to a package.json file via \fB--save\fR or \fB--save-dev\fR get prefixed.
.P
For example if a package has version \fB1.2.3\fR, by default its version is set to \fB^1.2.3\fR which allows minor upgrades for that package, but after \fBnpm
config set save-prefix='~'\fR it would be set to \fB~1.2.3\fR which only allows patch upgrades.
.SS "\fBsave-prod\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Save installed packages into \fBdependencies\fR specifically. This is useful if a package already exists in \fBdevDependencies\fR or \fBoptionalDependencies\fR, but you want to move it to be a non-optional production dependency.
.P
This is the default behavior if \fB--save\fR is true, and neither \fB--save-dev\fR or \fB--save-optional\fR are true.
.SS "\fBsbom-format\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: "cyclonedx" or "spdx"
.RE 0

.P
SBOM format to use when generating SBOMs.
.SS "\fBsbom-type\fR"
.RS 0
.IP \(bu 4
Default: "library"
.IP \(bu 4
Type: "library", "application", or "framework"
.RE 0

.P
The type of package described by the generated SBOM. For SPDX, this is the value for the \fBprimaryPackagePurpose\fR field. For CycloneDX, this is the value for the \fBtype\fR field.
.SS "\fBscope\fR"
.RS 0
.IP \(bu 4
Default: the scope of the current project, if any, or ""
.IP \(bu 4
Type: String
.RE 0

.P
Associate an operation with a scope for a scoped registry.
.P
Useful when logging in to or out of a private registry:
.P
.RS 2
.nf
# log in, linking the scope to the custom registry
npm login --scope=@mycorp --registry=https://registry.mycorp.com

# log out, removing the link and the auth token
npm logout --scope=@mycorp
.fi
.RE
.P
This will cause \fB@mycorp\fR to be mapped to the registry for future installation of packages specified according to the pattern \fB@mycorp/package\fR.
.P
This will also cause \fBnpm init\fR to create a scoped package.
.P
.RS 2
.nf
# accept all defaults, and create a package named "@foo/whatever",
# instead of just named "whatever"
npm init --scope=@foo --yes
.fi
.RE
.SS "\fBscript-shell\fR"
.RS 0
.IP \(bu 4
Default: '/bin/sh' on POSIX systems, 'cmd.exe' on Windows
.IP \(bu 4
Type: null or String
.RE 0

.P
The shell to use for scripts run with the \fBnpm exec\fR, \fBnpm run\fR and \fBnpm
init <package-spec>\fR commands.
.SS "\fBsearchexclude\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.RE 0

.P
Space-separated options that limit the results from search.
.SS "\fBsearchlimit\fR"
.RS 0
.IP \(bu 4
Default: 20
.IP \(bu 4
Type: Number
.RE 0

.P
Number of items to limit search results to. Will not apply at all to legacy searches.
.SS "\fBsearchopts\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.RE 0

.P
Space-separated options that are always passed to search.
.SS "\fBsearchstaleness\fR"
.RS 0
.IP \(bu 4
Default: 900
.IP \(bu 4
Type: Number
.RE 0

.P
The age of the cache, in seconds, before another registry request is made if using legacy search endpoint.
.SS "\fBshell\fR"
.RS 0
.IP \(bu 4
Default: SHELL environment variable, or "bash" on Posix, or "cmd.exe" on Windows
.IP \(bu 4
Type: String
.RE 0

.P
The shell to run for the \fBnpm explore\fR command.
.SS "\fBsign-git-commit\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, then the \fBnpm version\fR command will commit the new package version using \fB-S\fR to add a signature.
.P
Note that git requires you to have set up GPG keys in your git configs for this to work properly.
.SS "\fBsign-git-tag\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, then the \fBnpm version\fR command will tag the version using \fB-s\fR to add a signature.
.P
Note that git requires you to have set up GPG keys in your git configs for this to work properly.
.SS "\fBstrict-peer-deps\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to \fBtrue\fR, and \fB--legacy-peer-deps\fR is not set, then \fIany\fR conflicting \fBpeerDependencies\fR will be treated as an install failure, even if npm could reasonably guess the appropriate resolution based on non-peer dependency relationships.
.P
By default, conflicting \fBpeerDependencies\fR deep in the dependency graph will be resolved using the nearest non-peer dependency specification, even if doing so will result in some packages receiving a peer dependency outside the range set in their package's \fBpeerDependencies\fR object.
.P
When such an override is performed, a warning is printed, explaining the conflict and the packages involved. If \fB--strict-peer-deps\fR is set, then this warning is treated as a failure.
.SS "\fBstrict-ssl\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Whether or not to do SSL key validation when making requests to the registry via https.
.P
See also the \fBca\fR config.
.SS "\fBtag\fR"
.RS 0
.IP \(bu 4
Default: "latest"
.IP \(bu 4
Type: String
.RE 0

.P
If you ask npm to install a package and don't tell it a specific version, then it will install the specified tag.
.P
It is the tag added to the package@version specified in the \fBnpm dist-tag
add\fR command, if no explicit tag is given.
.P
When used by the \fBnpm diff\fR command, this is the tag used to fetch the tarball that will be compared with the local files by default.
.P
If used in the \fBnpm publish\fR command, this is the tag that will be added to the package submitted to the registry.
.SS "\fBtag-version-prefix\fR"
.RS 0
.IP \(bu 4
Default: "v"
.IP \(bu 4
Type: String
.RE 0

.P
If set, alters the prefix used when tagging a new version when performing a version increment using \fBnpm version\fR. To remove the prefix altogether, set it to the empty string: \fB""\fR.
.P
Because other tools may rely on the convention that npm version tags look like \fBv1.0.0\fR, \fIonly use this property if it is absolutely necessary\fR. In particular, use care when overriding this setting for public packages.
.SS "\fBtiming\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, writes timing information to a process specific json file in the cache or \fBlogs-dir\fR. The file name ends with \fB-timing.json\fR.
.P
You can quickly view it with this \fBjson\fR \fI\(lahttps://npm.im/json\(ra\fR command line: \fBcat ~/.npm/_logs/*-timing.json | npm exec -- json -g\fR.
.P
Timing information will also be reported in the terminal. To suppress this while still writing the timing file, use \fB--silent\fR.
.SS "\fBumask\fR"
.RS 0
.IP \(bu 4
Default: 0
.IP \(bu 4
Type: Octal numeric string in range 0000..0777 (0..511)
.RE 0

.P
The "umask" value to use when setting the file creation mode on files and folders.
.P
Folders and executables are given a mode which is \fB0o777\fR masked against this value. Other files are given a mode which is \fB0o666\fR masked against this value.
.P
Note that the underlying system will \fIalso\fR apply its own umask value to files and folders that are created, and npm does not circumvent this, but rather adds the \fB--umask\fR config to it.
.P
Thus, the effective default umask value on most POSIX systems is 0o22, meaning that folders and executables are created with a mode of 0o755 and other files are created with a mode of 0o644.
.SS "\fBunicode\fR"
.RS 0
.IP \(bu 4
Default: false on windows, true on mac/unix systems with a unicode locale, as defined by the \fBLC_ALL\fR, \fBLC_CTYPE\fR, or \fBLANG\fR environment variables.
.IP \(bu 4
Type: Boolean
.RE 0

.P
When set to true, npm uses unicode characters in the tree output. When false, it uses ascii characters instead of unicode glyphs.
.SS "\fBupdate-notifier\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
Set to false to suppress the update notification when using an older version of npm than the latest.
.SS "\fBusage\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Show short usage output about the command specified.
.SS "\fBuser-agent\fR"
.RS 0
.IP \(bu 4
Default: "npm/{npm-version} node/{node-version} {platform} {arch} workspaces/{workspaces} {ci}"
.IP \(bu 4
Type: String
.RE 0

.P
Sets the User-Agent request header. The following fields are replaced with their actual counterparts:
.RS 0
.IP \(bu 4
\fB{npm-version}\fR - The npm version in use
.IP \(bu 4
\fB{node-version}\fR - The Node.js version in use
.IP \(bu 4
\fB{platform}\fR - The value of \fBprocess.platform\fR
.IP \(bu 4
\fB{arch}\fR - The value of \fBprocess.arch\fR
.IP \(bu 4
\fB{workspaces}\fR - Set to \fBtrue\fR if the \fBworkspaces\fR or \fBworkspace\fR options are set.
.IP \(bu 4
\fB{ci}\fR - The value of the \fBci-name\fR config, if set, prefixed with \fBci/\fR, or an empty string if \fBci-name\fR is empty.
.RE 0

.SS "\fBuserconfig\fR"
.RS 0
.IP \(bu 4
Default: "~/.npmrc"
.IP \(bu 4
Type: Path
.RE 0

.P
The location of user-level configuration settings.
.P
This may be overridden by the \fBnpm_config_userconfig\fR environment variable or the \fB--userconfig\fR command line option, but may \fInot\fR be overridden by settings in the \fBglobalconfig\fR file.
.SS "\fBversion\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, output the npm version and exit successfully.
.P
Only relevant when specified explicitly on the command line.
.SS "\fBversions\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, output the npm version as well as node's \fBprocess.versions\fR map and the version in the current working directory's \fBpackage.json\fR file if one exists, and exit successfully.
.P
Only relevant when specified explicitly on the command line.
.SS "\fBviewer\fR"
.RS 0
.IP \(bu 4
Default: "man" on Posix, "browser" on Windows
.IP \(bu 4
Type: String
.RE 0

.P
The program to use to view help content.
.P
Set to \fB"browser"\fR to view html help content in the default web browser.
.SS "\fBwhich\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Number
.RE 0

.P
If there are multiple funding sources, which 1-indexed source URL to open.
.SS "\fBworkspace\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Enable running a command in the context of the configured workspaces of the current project while filtering by running only the workspaces defined by this configuration option.
.P
Valid values for the \fBworkspace\fR config are either:
.RS 0
.IP \(bu 4
Workspace names
.IP \(bu 4
Path to a workspace directory
.IP \(bu 4
Path to a parent workspace directory (will result in selecting all workspaces within that folder)
.RE 0

.P
When set for the \fBnpm init\fR command, this may be set to the folder of a workspace which does not yet exist, to create the folder and set it up as a brand new workspace within the project.
.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Set to true to run the command in the context of \fBall\fR configured workspaces.
.P
Explicitly setting this to false will cause commands like \fBinstall\fR to ignore workspaces altogether. When not set explicitly:
.RS 0
.IP \(bu 4
Commands that operate on the \fBnode_modules\fR tree (install, update, etc.) will link workspaces into the \fBnode_modules\fR folder. - Commands that do other things (test, exec, publish, etc.) will operate on the root project, \fIunless\fR one or more workspaces are specified in the \fBworkspace\fR config.
.RE 0

.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces-update\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, the npm cli will run an update after operations that may possibly change the workspaces installed to the \fBnode_modules\fR folder.
.SS "\fByes\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Automatically answer "yes" to any prompts that npm might print on the command line.
.SS "\fBalso\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null, "dev", or "development"
.IP \(bu 4
DEPRECATED: Please use --include=dev instead.
.RE 0

.P
When set to \fBdev\fR or \fBdevelopment\fR, this is an alias for \fB--include=dev\fR.
.SS "\fBcache-max\fR"
.RS 0
.IP \(bu 4
Default: Infinity
.IP \(bu 4
Type: Number
.IP \(bu 4
DEPRECATED: This option has been deprecated in favor of \fB--prefer-online\fR
.RE 0

.P
\fB--cache-max=0\fR is an alias for \fB--prefer-online\fR
.SS "\fBcache-min\fR"
.RS 0
.IP \(bu 4
Default: 0
.IP \(bu 4
Type: Number
.IP \(bu 4
DEPRECATED: This option has been deprecated in favor of \fB--prefer-offline\fR.
.RE 0

.P
\fB--cache-min=9999 (or bigger)\fR is an alias for \fB--prefer-offline\fR.
.SS "\fBcert\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.IP \(bu 4
DEPRECATED: \fBkey\fR and \fBcert\fR are no longer used for most registry operations. Use registry scoped \fBkeyfile\fR and \fBcertfile\fR instead. Example: //other-registry.tld/:keyfile=/path/to/key.pem //other-registry.tld/:certfile=/path/to/cert.crt
.RE 0

.P
A client certificate to pass when accessing the registry. Values should be in PEM format (Windows calls it "Base-64 encoded X.509 (.CER)") with newlines replaced by the string "\[rs]n". For example:
.P
.RS 2
.nf
cert="-----BEGIN CERTIFICATE-----\[rs]nXXXX\[rs]nXXXX\[rs]n-----END CERTIFICATE-----"
.fi
.RE
.P
It is \fInot\fR the path to a certificate file, though you can set a registry-scoped "certfile" path like "//other-registry.tld/:certfile=/path/to/cert.pem".
.SS "\fBdev\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.IP \(bu 4
DEPRECATED: Please use --include=dev instead.
.RE 0

.P
Alias for \fB--include=dev\fR.
.SS "\fBglobal-style\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.IP \(bu 4
DEPRECATED: This option has been deprecated in favor of \fB--install-strategy=shallow\fR
.RE 0

.P
Only install direct dependencies in the top level \fBnode_modules\fR, but hoist on deeper dependencies. Sets \fB--install-strategy=shallow\fR.
.SS "\fBinit.author.email\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.IP \(bu 4
DEPRECATED: Use \fB--init-author-email\fR instead.
.RE 0

.P
Alias for \fB--init-author-email\fR
.SS "\fBinit.author.name\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: String
.IP \(bu 4
DEPRECATED: Use \fB--init-author-name\fR instead.
.RE 0

.P
Alias for \fB--init-author-name\fR
.SS "\fBinit.author.url\fR"
.RS 0
.IP \(bu 4
Default: ""
.IP \(bu 4
Type: "" or URL
.IP \(bu 4
DEPRECATED: Use \fB--init-author-url\fR instead.
.RE 0

.P
Alias for \fB--init-author-url\fR
.SS "\fBinit.license\fR"
.RS 0
.IP \(bu 4
Default: "ISC"
.IP \(bu 4
Type: String
.IP \(bu 4
DEPRECATED: Use \fB--init-license\fR instead.
.RE 0

.P
Alias for \fB--init-license\fR
.SS "\fBinit.module\fR"
.RS 0
.IP \(bu 4
Default: "~/.npm-init.js"
.IP \(bu 4
Type: Path
.IP \(bu 4
DEPRECATED: Use \fB--init-module\fR instead.
.RE 0

.P
Alias for \fB--init-module\fR
.SS "\fBinit.version\fR"
.RS 0
.IP \(bu 4
Default: "1.0.0"
.IP \(bu 4
Type: SemVer string
.IP \(bu 4
DEPRECATED: Use \fB--init-version\fR instead.
.RE 0

.P
Alias for \fB--init-version\fR
.SS "\fBkey\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.IP \(bu 4
DEPRECATED: \fBkey\fR and \fBcert\fR are no longer used for most registry operations. Use registry scoped \fBkeyfile\fR and \fBcertfile\fR instead. Example: //other-registry.tld/:keyfile=/path/to/key.pem //other-registry.tld/:certfile=/path/to/cert.crt
.RE 0

.P
A client key to pass when accessing the registry. Values should be in PEM format with newlines replaced by the string "\[rs]n". For example:
.P
.RS 2
.nf
key="-----BEGIN PRIVATE KEY-----\[rs]nXXXX\[rs]nXXXX\[rs]n-----END PRIVATE KEY-----"
.fi
.RE
.P
It is \fInot\fR the path to a key file, though you can set a registry-scoped "keyfile" path like "//other-registry.tld/:keyfile=/path/to/key.pem".
.SS "\fBlegacy-bundling\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.IP \(bu 4
DEPRECATED: This option has been deprecated in favor of \fB--install-strategy=nested\fR
.RE 0

.P
Instead of hoisting package installs in \fBnode_modules\fR, install packages in the same manner that they are depended on. This may cause very deep directory structures and duplicate package installs as there is no de-duplicating. Sets \fB--install-strategy=nested\fR.
.SS "\fBonly\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null, "prod", or "production"
.IP \(bu 4
DEPRECATED: Use \fB--omit=dev\fR to omit dev dependencies from the install.
.RE 0

.P
When set to \fBprod\fR or \fBproduction\fR, this is an alias for \fB--omit=dev\fR.
.SS "\fBoptional\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.IP \(bu 4
DEPRECATED: Use \fB--omit=optional\fR to exclude optional dependencies, or \fB--include=optional\fR to include them.
.RE 0

.P
Default value does install optional deps unless otherwise omitted.
.P
Alias for --include=optional or --omit=optional
.SS "\fBproduction\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.IP \(bu 4
DEPRECATED: Use \fB--omit=dev\fR instead.
.RE 0

.P
Alias for \fB--omit=dev\fR
.SS "\fBshrinkwrap\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.IP \(bu 4
DEPRECATED: Use the --package-lock setting instead.
.RE 0

.P
Alias for --package-lock
.SS "See also"
.RS 0
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.IP \(bu 4
npm help scripts
.IP \(bu 4
npm help folders
.IP \(bu 4
npm help npm
.RE 0
