.TH "NPM-STARS" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-stars\fR - View packages marked as favorites
.SS "Synopsis"
.P
.RS 2
.nf
npm stars \[lB]<user>\[rB]
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
If you have starred a lot of neat things and want to find them again quickly this command lets you do just that.
.P
You may also want to see your friend's favorite packages, in this case you will most certainly enjoy this command.
.SS "Configuration"
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help star
.IP \(bu 4
npm help unstar
.IP \(bu 4
npm help view
.IP \(bu 4
npm help whoami
.IP \(bu 4
npm help adduser
.RE 0
