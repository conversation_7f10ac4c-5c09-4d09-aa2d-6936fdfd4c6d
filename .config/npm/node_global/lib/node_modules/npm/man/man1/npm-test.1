.TH "NPM-TEST" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-test\fR - Test a package
.SS "Synopsis"
.P
.RS 2
.nf
npm test \[lB]-- <args>\[rB]

aliases: tst, t
.fi
.RE
.SS "Description"
.P
This runs a predefined command specified in the \fB"test"\fR property of a package's \fB"scripts"\fR object.
.SS "Example"
.P
.RS 2
.nf
{
  "scripts": {
    "test": "node test.js"
  }
}
.fi
.RE
.P
.RS 2
.nf
npm test
> npm@x.x.x test
> node test.js

(test.js output would be here)
.fi
.RE
.SS "Configuration"
.SS "\fBignore-scripts\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, npm does not run scripts specified in package.json files.
.P
Note that commands explicitly intended to run a particular script, such as \fBnpm start\fR, \fBnpm stop\fR, \fBnpm restart\fR, \fBnpm test\fR, and \fBnpm run-script\fR will still run their intended script if \fBignore-scripts\fR is set, but they will \fInot\fR run any pre- or post-scripts.
.SS "\fBscript-shell\fR"
.RS 0
.IP \(bu 4
Default: '/bin/sh' on POSIX systems, 'cmd.exe' on Windows
.IP \(bu 4
Type: null or String
.RE 0

.P
The shell to use for scripts run with the \fBnpm exec\fR, \fBnpm run\fR and \fBnpm
init <package-spec>\fR commands.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help run-script
.IP \(bu 4
npm help scripts
.IP \(bu 4
npm help start
.IP \(bu 4
npm help restart
.IP \(bu 4
npm help stop
.RE 0
