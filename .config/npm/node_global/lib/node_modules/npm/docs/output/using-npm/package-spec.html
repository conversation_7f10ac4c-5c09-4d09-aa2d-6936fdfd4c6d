<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>package-spec</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----package-spec----1082">
    <span>package-spec</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Package name specifier</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#description">Description</a></li><li><a href="#package-name">Package name</a></li><li><a href="#aliases">Aliases</a></li><li><a href="#folders">Folders</a></li><li><a href="#tarballs">Tarballs</a></li><li><a href="#git-urls">git urls</a></li><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p>Commands like <code>npm install</code> and the dependency sections in the
<code>package.json</code> use a package name specifier.  This can be many different
things that all refer to a "package".  Examples include a package name,
git url, tarball, or local directory.  These will generally be referred
to as <code>&lt;package-spec&gt;</code> in the help output for the npm commands that use
this package name specifier.</p>
<h3 id="package-name">Package name</h3>
<ul>
<li><code>[&lt;@scope&gt;/]&lt;pkg&gt;</code></li>
<li><code>[&lt;@scope&gt;/]&lt;pkg&gt;@&lt;tag&gt;</code></li>
<li><code>[&lt;@scope&gt;/]&lt;pkg&gt;@&lt;version&gt;</code></li>
<li><code>[&lt;@scope&gt;/]&lt;pkg&gt;@&lt;version range&gt;</code></li>
</ul>
<p>Refers to a package by name, with or without a scope, and optionally
tag, version, or version range.  This is typically used in combination
with the <a href="../using-npm/config#registry.html">registry</a> config to refer to a
package in a registry.</p>
<p>Examples:</p>
<ul>
<li><code>npm</code></li>
<li><code>@npmcli/arborist</code></li>
<li><code>@npmcli/arborist@latest</code></li>
<li><code>npm@6.13.1</code></li>
<li><code>npm@^4.0.0</code></li>
</ul>
<h3 id="aliases">Aliases</h3>
<ul>
<li><code>&lt;alias&gt;@npm:&lt;name&gt;</code></li>
</ul>
<p>Primarily used by commands like <code>npm install</code> and in the dependency
sections in the <code>package.json</code>, this refers to a package by an alias.
The <code>&lt;alias&gt;</code> is the name of the package as it is reified in the
<code>node_modules</code> folder, and the <code>&lt;name&gt;</code> refers to a package name as
found in the configured registry.</p>
<p>See <code>Package name</code> above for more info on referring to a package by
name, and <a href="../using-npm/config#registry.html">registry</a> for configuring which
registry is used when referring to a package by name.</p>
<p>Examples:</p>
<ul>
<li><code>semver:@npm:@npmcli/semver-with-patch</code></li>
<li><code>semver:@npm:semver@7.2.2</code></li>
<li><code>semver:@npm:semver@legacy</code></li>
</ul>
<h3 id="folders">Folders</h3>
<ul>
<li><code>&lt;folder&gt;</code></li>
</ul>
<p>This refers to a package on the local filesystem.  Specifically this is
a folder with a <code>package.json</code> file in it.  This <em>should</em> always be
prefixed with a <code>/</code> or <code>./</code> (or your OS equivalent) to reduce confusion.
npm currently will parse a string with more than one <code>/</code> in it as a
folder, but this is legacy behavior that may be removed in a future
version.</p>
<p>Examples:</p>
<ul>
<li><code>./my-package</code></li>
<li><code>/opt/npm/my-package</code></li>
</ul>
<h3 id="tarballs">Tarballs</h3>
<ul>
<li><code>&lt;tarball file&gt;</code></li>
<li><code>&lt;tarball url&gt;</code></li>
</ul>
<p>Examples:</p>
<ul>
<li><code>./my-package.tgz</code></li>
<li><code>https://registry.npmjs.org/semver/-/semver-1.0.0.tgz</code></li>
</ul>
<p>Refers to a package in a tarball format, either on the local filesystem
or remotely via url.  This is the format that packages exist in when
uploaded to a registry.</p>
<h3 id="git-urls">git urls</h3>
<ul>
<li><code>&lt;git:// url&gt;</code></li>
<li><code>&lt;github username&gt;/&lt;github project&gt;</code></li>
</ul>
<p>Refers to a package in a git repo.  This can be a full git url, git
shorthand, or a username/package on GitHub.  You can specify a
git tag, branch, or other git ref by appending <code>#ref</code>.</p>
<p>Examples:</p>
<ul>
<li><code>https://github.com/npm/cli.git</code></li>
<li><code>**************:npm/cli.git</code></li>
<li><code>git+ssh://**************/npm/cli#v6.0.0</code></li>
<li><code>github:npm/cli#HEAD</code></li>
<li><code>npm/cli#c12ea07</code></li>
</ul>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="https://npm.im/npm-package-arg">npm-package-arg</a></li>
<li><a href="../using-npm/scope.html">scope</a></li>
<li><a href="../using-npm/config.html">config</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/package-spec.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>