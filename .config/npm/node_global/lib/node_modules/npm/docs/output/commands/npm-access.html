<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-access</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-access----1082">
    <span>npm-access</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Set access level on published packages</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#details">Details</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#json"><code>json</code></a></li><li><a href="#otp"><code>otp</code></a></li><li><a href="#registry"><code>registry</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm access list packages [&lt;user&gt;|&lt;scope&gt;|&lt;scope:team&gt;] [&lt;package&gt;]
npm access list collaborators [&lt;package&gt; [&lt;user&gt;]]
npm access get status [&lt;package&gt;]
npm access set status=public|private [&lt;package&gt;]
npm access set mfa=none|publish|automation [&lt;package&gt;]
npm access grant &lt;read-only|read-write&gt; &lt;scope:team&gt; [&lt;package&gt;]
npm access revoke &lt;scope:team&gt; [&lt;package&gt;]
</code></pre>
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p>Used to set access controls on private packages.</p>
<p>For all of the subcommands, <code>npm access</code> will perform actions on the packages
in the current working directory if no package name is passed to the
subcommand.</p>
<ul>
<li>
<p>public / restricted (deprecated):
Set a package to be either publicly accessible or restricted.</p>
</li>
<li>
<p>grant / revoke (deprecated):
Add or remove the ability of users and teams to have read-only or read-write
access to a package.</p>
</li>
<li>
<p>2fa-required / 2fa-not-required (deprecated):
Configure whether a package requires that anyone publishing it have two-factor
authentication enabled on their account.</p>
</li>
<li>
<p>ls-packages (deprecated):
Show all of the packages a user or a team is able to access, along with the
access level, except for read-only public packages (it won't print the whole
registry listing)</p>
</li>
<li>
<p>ls-collaborators (deprecated):
Show all of the access privileges for a package. Will only show permissions
for packages to which you have at least read access. If <code>&lt;user&gt;</code> is passed in,
the list is filtered only to teams <em>that</em> user happens to belong to.</p>
</li>
<li>
<p>edit (not implemented)</p>
</li>
</ul>
<h3 id="details">Details</h3>
<p><code>npm access</code> always operates directly on the current registry, configurable
from the command line using <code>--registry=&lt;registry url&gt;</code>.</p>
<p>Unscoped packages are <em>always public</em>.</p>
<p>Scoped packages <em>default to restricted</em>, but you can either publish them as
public using <code>npm publish --access=public</code>, or set their access as public using
<code>npm access public</code> after the initial publish.</p>
<p>You must have privileges to set the access of a package:</p>
<ul>
<li>You are an owner of an unscoped or scoped package.</li>
<li>You are a member of the team that owns a scope.</li>
<li>You have been given read-write privileges for a package, either as a member
of a team or directly as an owner.</li>
</ul>
<p>If you have two-factor authentication enabled then you'll be prompted to provide a second factor, or may use the <code>--otp=...</code> option to specify it on
the command line.</p>
<p>If your account is not paid, then attempts to publish scoped packages will
fail with an HTTP 402 status code (logically enough), unless you use
<code>--access=public</code>.</p>
<p>Management of teams and team memberships is done with the <code>npm team</code> command.</p>
<h3 id="configuration">Configuration</h3>
<h4 id="json"><code>json</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<ul>
<li>In <code>npm pkg set</code> it enables parsing set values with JSON.parse() before
saving them to your <code>package.json</code>.</li>
</ul>
<p>Not supported by all npm commands.</p>
<h4 id="otp"><code>otp</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or String</li>
</ul>
<p>This is a one-time password from a two-factor authenticator. It's needed
when publishing or changing package permissions with <code>npm access</code>.</p>
<p>If not set, and a registry response fails with a challenge for a one-time
password, npm will prompt on the command line for one.</p>
<h4 id="registry"><code>registry</code></h4>
<ul>
<li>Default: "<a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a>"</li>
<li>Type: URL</li>
</ul>
<p>The base URL of the npm registry.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="https://npm.im/libnpmaccess"><code>libnpmaccess</code></a></li>
<li><a href="../commands/npm-team.html">npm team</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-access.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>