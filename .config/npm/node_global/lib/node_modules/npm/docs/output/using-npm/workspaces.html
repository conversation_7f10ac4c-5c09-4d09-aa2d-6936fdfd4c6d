<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>workspaces</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----workspaces----1082">
    <span>workspaces</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Working with workspaces</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#description">Description</a></li><li><a href="#defining-workspaces">Defining workspaces</a></li><li><a href="#getting-started-with-workspaces">Getting started with workspaces</a></li><li><a href="#adding-dependencies-to-a-workspace">Adding dependencies to a workspace</a></li><li><a href="#using-workspaces">Using workspaces</a></li><li><a href="#running-commands-in-the-context-of-workspaces">Running commands in the context of workspaces</a></li><li><a href="#ignoring-missing-scripts">Ignoring missing scripts</a></li><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p><strong>Workspaces</strong> is a generic term that refers to the set of features in the
npm cli that provides support for managing multiple packages from your local
file system from within a singular top-level, root package.</p>
<p>This set of features makes up for a much more streamlined workflow handling
linked packages from the local file system. It automates the linking process
as part of <code>npm install</code> and removes the need to manually use <code>npm link</code> in
order to add references to packages that should be symlinked into the current
<code>node_modules</code> folder.</p>
<p>We also refer to these packages being auto-symlinked during <code>npm install</code> as a
single <strong>workspace</strong>, meaning it's a nested package within the current local
file system that is explicitly defined in the <a href="../configuring-npm/package-json#workspaces.html"><code>package.json</code></a>
<code>workspaces</code> configuration.</p>
<h3 id="defining-workspaces">Defining workspaces</h3>
<p>Workspaces are usually defined via the <code>workspaces</code> property of the
<a href="../configuring-npm/package-json#workspaces.html"><code>package.json</code></a> file, e.g:</p>
<pre><code class="language-json">{
  "name": "my-workspaces-powered-project",
  "workspaces": [
    "packages/a"
  ]
}
</code></pre>
<p>Given the above <code>package.json</code> example living at a current working
directory <code>.</code> that contains a folder named <code>packages/a</code> that itself contains
a <code>package.json</code> inside it, defining a Node.js package, e.g:</p>
<pre><code>.
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
</code></pre>
<p>The expected result once running <code>npm install</code> in this current working
directory <code>.</code> is that the folder <code>packages/a</code> will get symlinked to the
<code>node_modules</code> folder of the current working dir.</p>
<p>Below is a post <code>npm install</code> example, given that same previous example
structure of files and folders:</p>
<pre><code>.
+-- node_modules
|  `-- a -&gt; ../packages/a
+-- package-lock.json
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
</code></pre>
<h3 id="getting-started-with-workspaces">Getting started with workspaces</h3>
<p>You may automate the required steps to define a new workspace using
<a href="../commands/npm-init.html">npm init</a>. For example in a project that already has a
<code>package.json</code> defined you can run:</p>
<pre><code>npm init -w ./packages/a
</code></pre>
<p>This command will create the missing folders and a new <code>package.json</code>
file (if needed) while also making sure to properly configure the
<code>"workspaces"</code> property of your root project <code>package.json</code>.</p>
<h3 id="adding-dependencies-to-a-workspace">Adding dependencies to a workspace</h3>
<p>It's possible to directly add/remove/update dependencies of your workspaces
using the <a href="../using-npm/config#workspace.html"><code>workspace</code> config</a>.</p>
<p>For example, assuming the following structure:</p>
<pre><code>.
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
   `-- b
       `-- package.json
</code></pre>
<p>If you want to add a dependency named <code>abbrev</code> from the registry as a
dependency of your workspace <strong>a</strong>, you may use the workspace config to tell
the npm installer that package should be added as a dependency of the provided
workspace:</p>
<pre><code>npm install abbrev -w a
</code></pre>
<p>Note: other installing commands such as <code>uninstall</code>, <code>ci</code>, etc will also
respect the provided <code>workspace</code> configuration.</p>
<h3 id="using-workspaces">Using workspaces</h3>
<p>Given the <a href="https://nodejs.org/dist/latest-v14.x/docs/api/modules.html#modules_all_together">specifics of how Node.js handles module resolution</a> it's possible to consume any defined workspace
by its declared <code>package.json</code> <code>name</code>. Continuing from the example defined
above, let's also create a Node.js script that will require the workspace <code>a</code>
example module, e.g:</p>
<pre><code>// ./packages/a/index.js
module.exports = 'a'

// ./lib/index.js
const moduleA = require('a')
console.log(moduleA) // -&gt; a
</code></pre>
<p>When running it with:</p>
<p><code>node lib/index.js</code></p>
<p>This demonstrates how the nature of <code>node_modules</code> resolution allows for
<strong>workspaces</strong> to enable a portable workflow for requiring each <strong>workspace</strong>
in such a way that is also easy to <a href="../commands/npm-publish.html">publish</a> these
nested workspaces to be consumed elsewhere.</p>
<h3 id="running-commands-in-the-context-of-workspaces">Running commands in the context of workspaces</h3>
<p>You can use the <code>workspace</code> configuration option to run commands in the context
of a configured workspace.
Additionally, if your current directory is in a workspace, the <code>workspace</code>
configuration is implicitly set, and <code>prefix</code> is set to the root workspace.</p>
<p>Following is a quick example on how to use the <code>npm run</code> command in the context
of nested workspaces. For a project containing multiple workspaces, e.g:</p>
<pre><code>.
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
   `-- b
       `-- package.json
</code></pre>
<p>By running a command using the <code>workspace</code> option, it's possible to run the
given command in the context of that specific workspace. e.g:</p>
<pre><code>npm run test --workspace=a
</code></pre>
<p>You could also run the command within the workspace.</p>
<pre><code>cd packages/a &amp;&amp; npm run test
</code></pre>
<p>Either will run the <code>test</code> script defined within the
<code>./packages/a/package.json</code> file.</p>
<p>Please note that you can also specify this argument multiple times in the
command-line in order to target multiple workspaces, e.g:</p>
<pre><code>npm run test --workspace=a --workspace=b
</code></pre>
<p>Or run the command for each workspace within the 'packages' folder:</p>
<pre><code>npm run test --workspace=packages
</code></pre>
<p>It's also possible to use the <code>workspaces</code> (plural) configuration option to
enable the same behavior but running that command in the context of <strong>all</strong>
configured workspaces. e.g:</p>
<pre><code>npm run test --workspaces
</code></pre>
<p>Will run the <code>test</code> script in both <code>./packages/a</code> and <code>./packages/b</code>.</p>
<p>Commands will be run in each workspace in the order they appear in your <code>package.json</code></p>
<pre><code>{
  "workspaces": [ "packages/a", "packages/b" ]
}
</code></pre>
<p>Order of run is different with:</p>
<pre><code>{
  "workspaces": [ "packages/b", "packages/a" ]
}
</code></pre>
<h3 id="ignoring-missing-scripts">Ignoring missing scripts</h3>
<p>It is not required for all of the workspaces to implement scripts run with the <code>npm run</code> command.</p>
<p>By running the command with the <code>--if-present</code> flag, npm will ignore workspaces missing target script.</p>
<pre><code>npm run test --workspaces --if-present
</code></pre>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
<li><a href="../commands/npm-run-script.html">npm run-script</a></li>
<li><a href="../using-npm/config.html">config</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/workspaces.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>