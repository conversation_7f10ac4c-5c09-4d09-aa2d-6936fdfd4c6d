{"name": "nextjs-13-replit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"firebase": "^10.12.4", "firebase-admin": "^12.2.0", "google-spreadsheet": "^4.1.4", "next": "^14.2.5", "react": "^18", "react-dom": "^18", "react-firebase-hooks": "^5.1.1", "resend": "^4.5.1", "sweetalert2": "^11.12.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}