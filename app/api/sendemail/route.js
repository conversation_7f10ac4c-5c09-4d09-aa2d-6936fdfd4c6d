export const dynamic = 'force-dynamic';
import { Resend } from 'resend';
import { NextResponse } from "next/server";
import { adminDb } from "../../../lib/firebaseAdmin.js";
import { matchday, extravaganzaWeek } from "../../../constants/settings";

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY);

export async function GET(req) {
  // Fetch matches from the API
  const API_KEY = process.env.NEXT_PUBLIC_FOOTBALL_DATA_API_KEY;
  const response = await fetch(
    `https://api.football-data.org/v4/competitions/PL/matches?matchday=${matchday}`,
    {
      headers: { "X-Auth-Token": API_KEY },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch matches");
  }

  // Get selected matches for the week
  const selectedMatchesDoc = await adminDb.collection("matches").doc('matchday' + matchday).get();
  if (!selectedMatchesDoc.exists) {
    throw new Error("No matches selected for this matchday");
  }

  const matchData = await response.json();
  const matches = matchData.matches;

  // Create a map of all matches for easy lookup
  const matchesMap = matches.reduce((acc, match) => {
    acc[match.id] = match;
    return acc;
  }, {});

  // Get the selected matches data
  const selectedMatches = selectedMatchesDoc.data().matches;
  const allSelectedMatchIds = [...selectedMatches.regular, selectedMatches.special];

  // Create match mappings only for selected matches
  const matchMappings = allSelectedMatchIds.map(matchId => {
    const match = matchesMap[parseInt(matchId)];
    return {
      matchId: matchId.toString(),
      homeTeam: match.homeTeam.name,
      awayTeam: match.awayTeam.name,
      isSpecial: matchId.toString() === selectedMatches.special.toString()
    };
  });

  // need to get the predictions for each user
  const users = await adminDb.collection("users").get();
  const userIds = users.docs.map((doc) => doc.id);
  const userNames = users.docs.map((doc) => doc.data().name);

  const userPredictions = await adminDb
    .collection("predictions")
    .where("userId", "in", userIds)
    .where("matchday", "==", matchday)
    .get();
  const userPredictionsData = userPredictions.docs.map((doc) => doc.data());

  // loop through each user and show their predictions with their username, match ids are in the predictions map within userPredictionsData
  let predictionsList = [];

  userIds.forEach((userId, index) => {
    const userName = userNames[index];
    const userPrediction = userPredictionsData.find(
      (pred) => pred.userId === userId
    );

    if (userPrediction) {
      let userPredictionText = `${userName}\n`;

      matchMappings.forEach((match) => {
        const prediction = userPrediction.predictions[match.matchId];
        const result =
          prediction === "HOME_WIN"
            ? match.homeTeam
            : prediction === "AWAY_WIN"
            ? match.awayTeam
            : "Draw";

        if (match.isSpecial || extravaganzaWeek) {
          const homeScores =
            userPrediction.homeScores?.[match.matchId] || "N/A";
          const awayScores =
            userPrediction.awayScores?.[match.matchId] || "N/A";
          userPredictionText += `- ${match.homeTeam} vs ${match.awayTeam} (${result}) [${homeScores}-${awayScores}]\n`;
        } else {
          userPredictionText += `- ${match.homeTeam} vs ${match.awayTeam} (${result})\n`;
        }
      });

      predictionsList.push(userPredictionText);
    }
  });

  console.log(predictionsList.join("\n"));

  // Send email
  const msg = {
    from: 'Tyler Hozie <<EMAIL>>',
    to: ['<EMAIL>'],
    subject: 'Match ' + matchday + ' Predictions',
    text: predictionsList.join("\n"),
    html: `<pre style="font-family: Arial;">${predictionsList.join("\n")}</pre>`,
  };

  try {
    await resend.emails.send(msg);
    console.log('Email sent');
  } catch (error) {
    console.error('Error sending email:', error);
  }

  // return the response as text
  return new Response(predictionsList.join("\n"));
}
